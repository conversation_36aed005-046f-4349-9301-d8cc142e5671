<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
table{border-right:1px solid #ccc;border-bottom:1px solid #ccc}
table td{border-left:1px solid #ccc;border-top:1px solid #ccc;padding-left:8px;padding-right:8px;}
*{font-size:14px; }
.important_text{color:#369; font-weight: bold;}
.log_info{color:#000; }
.normal_info{color:#666; }
.maintitle{font-size: 18px;}
.title{background-color:#369; border:1px solid #369; color:#fff}
    </style>
</head>
    <body>
    <div><br/> <br/> <br/></div><table style='line-height:28px' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' rowspan='5' class='maintitle' width='25%'>测试包详情</td>
        <td class='maintitle' width='25%'>Google Services 测试</td>
        <td width='50%'><span class='important_text'>v1.0.0-Google配置测试</span></td>
    </tr>
</table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td class='normal_info' width='25%'>apk地址</td>
        <td class='important_text' width='75%'><a href='/test/app.apk'>/test/app.apk</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>mapping文件地址</td>
        <td class='important_text' width='75%'><a href='/test/mapping.txt'>/test/mapping.txt</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>apk的Http地址</td>
        <td class='important_text' width='75%'><a href='http://test.com/app.apk'>http://test.com/app.apk</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>mapping的Http文件地址</td>
        <td class='important_text' width='75%'><a href='http://test.com/mapping.txt'>http://test.com/mapping.txt</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>类型</td>
        <td class='important_text' width='75%'>debug</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>渠道</td>
        <td class='important_text' width='75%'>google_test</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>VersionCode</td>
        <td width='75%' class='important_text'>1</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>VersionName</td>
        <td width='75%' class='important_text'>1.0.0</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>打包日期</td>
        <td width='75%'>2025-08-04 18:31:09</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>文件大小(Byte)</td>
        <td width='75%'>10485760</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>备注</td>
        <td width='75%'>专门测试Google Services配置显示功能</td>
    </tr>
</table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
                                                                                                                     <tr>
                                                                                                                     <td align='center' colspan='4' class='title' width='100%'>产品需求</td>
    </tr>

    <tr style="background-color:gray">
        <td align='center' width='10%'>序号</td>
        <td align='center' width='40%'>功能</td>
        <td align='center' width='30%'>状态</td>
        <td align='center' width='20%'>负责人</td>
    </tr>
</tr><tr>
        <td align='center' width='10%'>1</td>
        <td align='left' width='50%'>Google Services集成</td>
        <td align='center' width='20%'>提测</td>
        <td align='center' width='20%'>测试员</td>
    </tr><tr>
        <td align='center' width='10%'> </td>
        <td align='center' width='50%'> </td>
        <td align='center' width='20%'> </td>
        <td align='center' width='20%'> </td>
    </tr>
</table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' colspan='3' class='title' width='100%'>测试重点</td>
    </tr>
    <tr>
        <td>测试Google Services配置是否正确显示</br></td>
    </tr>
</table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
        <tr>
            <td align='center' colspan='3' class='title' width='100%'>配置[ test_config.txt ]</td>
        </tr>
        <tr>
            <td>GOOGLE_SERVICES_ENABLED=true</br></td>
        </tr>
    </table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
        <tr>
            <td align='center' colspan='3' class='title' width='100%'>应用配置</td>
        </tr>
        <tr>
            <td>应用名称: Google Services 测试应用</br></td>
        </tr>
    </table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
        <tr>
            <td align='center' colspan='3' class='title' width='100%'>Google Services 配置</td>
        </tr>
        <tr>
            <td>{</br>&nbsp;&nbsp;"client":&nbsp;[</br>&nbsp;&nbsp;&nbsp;&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"services":&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"appinvite_service":&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"other_platform_oauth_client":&nbsp;[]</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;},&nbsp;</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"oauth_client":&nbsp;[],&nbsp;</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"api_key":&nbsp;[</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"current_key":&nbsp;"AIzaSyA0snh3FUGtvtnW-vil_eeGxdZjbR0vITM"</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;],&nbsp;</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"client_info":&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"mobilesdk_app_id":&nbsp;"1:1030427709509:android:b0592dd8370c718d8b132c",&nbsp;</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"android_client_info":&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"package_name":&nbsp;"ifitness.home.workout.body.exercise"</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</br>&nbsp;&nbsp;&nbsp;&nbsp;}</br>&nbsp;&nbsp;],&nbsp;</br>&nbsp;&nbsp;"configuration_version":&nbsp;"1",&nbsp;</br>&nbsp;&nbsp;"project_info":&nbsp;{</br>&nbsp;&nbsp;&nbsp;&nbsp;"storage_bucket":&nbsp;"clean-master-box.appspot.com",&nbsp;</br>&nbsp;&nbsp;&nbsp;&nbsp;"project_id":&nbsp;"clean-master-box",&nbsp;</br>&nbsp;&nbsp;&nbsp;&nbsp;"project_number":&nbsp;"1030427709509"</br>&nbsp;&nbsp;}</br>}</br></td>
        </tr>
    </table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
                    <tr>
                        <td align='center' colspan='3' class='title' width='100%'>提交记录-代码工程</td>
                    </tr>
                     <tr>
                     <tr style="background-color:gray">
                <td colspan='3' width='100%'>[main] ======></td>
                </tr>
             </tr>
         </table><table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
                    <tr>
                        <td align='center' colspan='3' class='title' width='100%'>提交记录-Build工程</td>
                    </tr>
                     <tr>
                     <tr style="background-color:gray">
                <td colspan='3' width='100%'>[main] ======></td>
                </tr>
             </tr>
         </table> </body>
</html>