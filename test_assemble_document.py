#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
测试脚本：用于测试 assemble_document.py 脚本的功能
这个脚本会创建一个 assemble_document 实例并生成HTML文档
"""

import os
import sys
from datetime import datetime

# 添加 assemble_tools 目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
assemble_tools_dir = os.path.join(current_dir, "assemble_tools")
sys.path.insert(0, assemble_tools_dir)

from assemble_document import assemble_document, browser

def create_test_files():
    """创建测试所需的文件"""
    assemble_tools_dir = os.path.join(current_dir, "assemble_tools")
    
    # 创建测试需求文件
    test_desc_file = os.path.join(assemble_tools_dir, "test_produce_demand.txt")
    with open(test_desc_file, 'w') as f:
        f.write("# 产品需求测试文件\n")
        f.write("登录功能优化_提测_张三\n")
        f.write("支付流程改进_开发中_李四\n")
        f.write("界面UI更新_提测_王五\n")

    # 创建测试重点文件
    test_details_file = os.path.join(assemble_tools_dir, "test_details.txt")
    with open(test_details_file, 'w') as f:
        f.write("# 测试重点说明\n")
        f.write("1. 重点测试登录功能的稳定性\n")
        f.write("2. 验证支付流程的安全性\n")
        f.write("3. 检查新UI界面的兼容性\n")
        f.write("4. 测试Google Services集成是否正常\n")

    # 创建应用配置文件
    product_file = os.path.join(current_dir, "product_config.txt")
    with open(product_file, 'w') as f:
        f.write("# 应用配置信息\n")
        f.write("应用名称: 测试应用\n")
        f.write("包名: com.test.app\n")
        f.write("目标SDK: 33\n")
        f.write("最小SDK: 21\n")
    
    # 创建配置目录和文件
    cfg_dir = os.path.join(current_dir, "config")
    if not os.path.exists(cfg_dir):
        os.makedirs(cfg_dir)
    
    config_file = os.path.join(cfg_dir, "app_config.txt")
    with open(config_file, 'w') as f:
        f.write("# 应用配置\n")
        f.write("DEBUG_MODE=false\n")
        f.write("API_URL=https://api.example.com\n")
        f.write("TIMEOUT=30\n")

    # 创建混淆文件
    proguard_file = os.path.join(current_dir, "proguard-user.txt")
    with open(proguard_file, 'w') as f:
        f.write("# 混淆配置\n")
        f.write("-keep class com.test.** { *; }\n")
        f.write("-dontwarn okhttp3.**\n")
    
    return {
        'test_desc_file': "test_produce_demand.txt",
        'test_details_file': "test_details.txt",
        'product_file': "product_config.txt",
        'cfg_dir': "config",
        'proguard_file': "proguard-user.txt"
    }

def main():
    """主函数：创建测试数据并生成HTML文档"""
    print("开始测试 assemble_document 脚本...")
    
    # 创建测试文件
    test_files = create_test_files()
    
    # 获取当前时间
    current_time = datetime.now()
    package_date = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 创建 assemble_document 实例
    doc = assemble_document(
        build_project_root=current_dir,  # 项目根目录
        build_produce_name="测试应用",  # 产品名称
        build_info="v1.0.0-测试版本",  # 构建信息
        build_apk_address="/path/to/test.apk",  # APK地址
        build_mapping_address="/path/to/mapping.txt",  # mapping文件地址
        http_build_apk_address="http://example.com/test.apk",  # HTTP APK地址
        http_build_mapping_address="http://example.com/mapping.txt",  # HTTP mapping地址
        build_type="debug",  # 构建类型
        build_channel="test",  # 渠道
        build_versionCode="100",  # 版本号
        build_versionName="1.0.0",  # 版本名称
        build_package_date=package_date,  # 打包日期
        build_package_size="15728640",  # 文件大小（字节）
        build_remark="这是一个测试版本，包含Google Services配置展示功能",  # 备注
        build_produce_demand_file=test_files['test_desc_file'],  # 需求文件
        build_test_details_file=test_files['test_details_file'],  # 测试重点文件
        build_cfg_file=test_files['cfg_dir'],  # 配置文件目录
        build_product_file=test_files['product_file'],  # 产品配置文件
        build_version_commit_count=5,  # 提交记录数量
        build_proguard_file=test_files['proguard_file']  # 混淆文件
    )
    
    print("正在生成HTML文档...")
    
    # 生成HTML内容
    html_content = doc.build()
    
    # 保存HTML文件
    output_file = "test_output.html"
    with open(output_file, 'w') as f:
        f.write(html_content.encode('utf-8'))

    print("HTML文档已生成: " + output_file)
    print("文档包含以下内容:")
    print("- 测试包详情")
    print("- 产品需求列表")
    print("- 测试重点说明")
    print("- 应用配置信息")
    print("- Google Services 配置 (新增)")
    print("- Git提交记录")

    # 在浏览器中打开HTML文件
    print("\n正在浏览器中打开 " + output_file + "...")
    browser(html_content)

    print("\n测试完成！请检查生成的HTML文档中是否正确显示了google-services.json的内容。")

if __name__ == '__main__':
    main()
