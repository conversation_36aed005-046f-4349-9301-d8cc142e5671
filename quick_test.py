#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
快速测试脚本：用于快速测试 assemble_document.py 的 Google Services 配置显示功能
"""

import os
import sys
from datetime import datetime

# 添加 assemble_tools 目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
assemble_tools_dir = os.path.join(current_dir, "assemble_tools")
sys.path.insert(0, assemble_tools_dir)

from assemble_document import assemble_document, browser

def main():
    """快速测试主函数"""
    print("快速测试 Google Services 配置显示功能...")
    
    # 创建最小化的测试文件
    test_desc_file = os.path.join(assemble_tools_dir, "quick_test_demand.txt")
    with open(test_desc_file, 'w') as f:
        f.write("Google Services集成_提测_测试员\n")
    
    test_details_file = os.path.join(assemble_tools_dir, "quick_test_details.txt")
    with open(test_details_file, 'w') as f:
        f.write("测试Google Services配置是否正确显示\n")
    
    product_file = os.path.join(current_dir, "quick_product_config.txt")
    with open(product_file, 'w') as f:
        f.write("应用名称: Google Services 测试应用\n")
    
    # 创建配置目录
    cfg_dir = os.path.join(current_dir, "quick_config")
    if not os.path.exists(cfg_dir):
        os.makedirs(cfg_dir)
    
    config_file = os.path.join(cfg_dir, "test_config.txt")
    with open(config_file, 'w') as f:
        f.write("GOOGLE_SERVICES_ENABLED=true\n")
    
    proguard_file = os.path.join(current_dir, "quick_proguard.txt")
    with open(proguard_file, 'w') as f:
        f.write("-keep class com.google.** { *; }\n")
    
    # 创建 assemble_document 实例
    doc = assemble_document(
        build_project_root=current_dir,
        build_produce_name="Google Services 测试",
        build_info="v1.0.0-Google配置测试",
        build_apk_address="/test/app.apk",
        build_mapping_address="/test/mapping.txt",
        http_build_apk_address="http://test.com/app.apk",
        http_build_mapping_address="http://test.com/mapping.txt",
        build_type="debug",
        build_channel="google_test",
        build_versionCode="1",
        build_versionName="1.0.0",
        build_package_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        build_package_size="10485760",
        build_remark="专门测试Google Services配置显示功能",
        build_produce_demand_file="quick_test_demand.txt",
        build_test_details_file="quick_test_details.txt",
        build_cfg_file="quick_config",
        build_product_file="quick_product_config.txt",
        build_version_commit_count=3,
        build_proguard_file="quick_proguard.txt"
    )
    
    print("正在生成HTML文档...")
    html_content = doc.build()
    
    # 保存HTML文件
    output_file = "google_services_test.html"
    with open(output_file, 'w') as f:
        f.write(html_content.encode('utf-8'))
    
    print("HTML文档已生成: " + output_file)
    print("\n重点检查项目:")
    print("1. 是否有 'Google Services 配置' 标题")
    print("2. 是否正确显示了 google-services.json 的内容")
    print("3. JSON格式是否清晰可读")
    print("4. 是否包含项目信息、客户端信息、API密钥等")
    
    # 在浏览器中打开
    print("\n正在浏览器中打开文档...")
    browser(html_content)
    
    print("\n测试完成！")
    
    # 清理测试文件
    try:
        os.remove(test_desc_file)
        os.remove(test_details_file)
        os.remove(product_file)
        os.remove(config_file)
        os.remove(proguard_file)
        os.rmdir(cfg_dir)
        print("测试文件已清理")
    except:
        print("清理测试文件时出现问题，请手动删除")

if __name__ == '__main__':
    main()
