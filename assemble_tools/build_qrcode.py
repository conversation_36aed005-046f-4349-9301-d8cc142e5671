import os

try:
    import qrcode
except Exception, e:
    print(e)
    os.system("sudo pip install qrcode")
    import qrcode
from PIL import Image

qr = qrcode.QRCode(
    version=7,
    error_correction=qrcode.constants.ERROR_CORRECT_L,
    box_size=10,
    border=4
)


def buildQrcode(path, url):
    logoPath = "./android/res/mipmap-xxhdpi/ic_launcher.png"
    if os.path.exists(logoPath):
        print 'logo:' + logoPath
        return buildQrcodeWithLogo(path, url, logoPath)
    logoPath = "./app/res/mipmap-xxhdpi/ic_launcher.png"
    if os.path.exists(logoPath):
        print 'logo:' + logoPath
        return buildQrcodeWithLogo(path, url, logoPath)
    else:
        print 'no logo:'
        return buildQrcodeNoLog(path, url)

def buildQrcodeNoLog(path, url):
    qr.add_data(url)
    qr.make(fit=True)
    img = qr.make_image()
    path = path + "/out/last_apk_qrcode.png"
    img.save(path)
    return path

def buildQrcodeWithLogo(path, url,logoPath):
    qr.add_data(url)
    qr.make(fit=True)
    img = qr.make_image()
    img = img.convert("RGBA")
    #logo="D:/favicon.jpg"

    icon = Image.open(logoPath)
    img_w,img_h = img.size
    factor = 5
    size_w = int(img_w / factor)
    size_h = int(img_h / factor)

    icon_w,icon_h = icon.size
    if icon_w >size_w:
        icon_w = size_w
    if icon_h > size_h:
        icon_h = size_h
    icon = icon.resize((icon_w,icon_h),Image.ANTIALIAS)

    w = int((img_w - icon_w)/2)
    h = int((img_h - icon_h)/2)
    icon = icon.convert("RGBA")
    img.paste(icon,(w,h),icon)
    #img.show()
    path = path + "/out/last_apk_qrcode.png"
    img.save(path)
    return path