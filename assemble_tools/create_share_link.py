#!/usr/bin/python
# -*- coding:utf-8 -*-

import datetime
import os
try:
    import requests
except Exception as e:
    print(e)
    os.system("pip install requests")
    import requests


def login(base_url, username, password):
    login_url = '{}/webapi/auth.cgi'.format(base_url)
    data = {
        'api': 'SYNO.API.Auth',
        'method': 'login',
        'version': '4',
        'account': username,
        'passwd': password,
        'session': 'FileStation'
    }
    response = requests.post(login_url, data=data)
    data = response.json()

    if data['success']:
        session_id = data['data']['sid']
        print("login success，SID：", session_id)
        return session_id
    else:
        print("login fail：", data)
        return None


def create_share_link(base_url, session_id, path, expiration_date):
    share_url = '{}/webapi/auth.cgi'.format(base_url)
    data = {
        'api': 'SYNO.FileStation.Sharing',
        'method': 'create',
        'version': '3',
        'path': path,
        'type': 'folder',
        'date_expired': '"{}"'.format(expiration_date),
        'protect_groups': '["17snail"]',
        'protect_type': 'user',
        '_sid': session_id
    }
    response = requests.post(share_url, data=data)
    data = response.json()
    print("data:" + str(data))

    if data['success']:
        share_link = data['data']['links'][0]['url']
        print("已创建共享链接：", share_link)
        return share_link
    else:
        print("无法创建共享链接：", data)
        return None


def edit_share_link(base_url, session_id, share_link_id, expiration_date):
    share_url = '{}/webapi/auth.cgi'.format(base_url)
    data = {
        'api': 'SYNO.FileStation.Sharing',
        'method': 'edit',
        'version': '3',
        'id': share_link_id,
        'date_expired': expiration_date,
        '_sid': session_id
    }
    response = requests.post(share_url, data=data)
    data = response.json()
    print(data)


def list_share_links(base_url, session_id):
    share_url = '{}/webapi/auth.cgi'.format(base_url)
    data = {
        'api': 'SYNO.FileStation.Sharing',
        'method': 'list',
        'version': '3',
        'offset': '0',
        'limit': '0',
        '_sid': session_id
    }
    response = requests.post(share_url, data=data)
    data = response.json()
    if data['success']:
        share_links = [link['url'] for link in data['data']['links']]
        share_links_id = [id['id'] for id in data['data']['links']]
        print("share link：", share_links)
        return share_links_id
    else:
        print("no share link：", data)

def clean_up_dead_share_link(base_url, session_id):
    share_url = '{}/webapi/entry.cgi'.format(base_url)
    data = {
        'api': 'SYNO.FileStation.Sharing',
        'method': 'clear_invalid',
        'version': '3',
        '_sid': session_id
    }
    response = requests.post(share_url, data=data)
    data = response.json()

    if data['success']:
        print("所有失效链接已清除")
    else:
        print("无法清除失效链接：", data)


def logout(base_url, session_id):
    logout_url = '{}/webapi/auth.cgi'.format(base_url)
    data = {
        'api': 'SYNO.API.Auth',
        'method': 'logout',
        'version': '4',
        'session': 'FileStation',
        '_sid': session_id
    }
    requests.post(logout_url, data=data)


# 主程序
def main(path, ftp_username, ftp_userpassword):
    base_url = 'http://***********:5000'
    username = ftp_username
    password = ftp_userpassword

    # path = '/Release/ftpapk/fightapk/637-WordKing-iOS/1.0.2/637-WordKing-iOS_dev_1.0.2_4_202305221958.ipa'
    # 获取当前日期
    current_date = datetime.datetime.now().date()
    # 计算一周后的日期, 即过期时间为一周
    expiration_date = current_date + datetime.timedelta(weeks=1)

    # 创建共享
    session_id = login(base_url, username, password)
    if session_id:
        # 创建共享链接,过期时间为一周
        response = share_link = create_share_link(base_url, session_id, path, expiration_date)
        print("response:" + str(response))
        # 查看所有共享链接
        list_share_links(base_url, session_id)
        clean_up_dead_share_link(base_url, session_id)
        return response
    logout(base_url, session_id)


# 运行主程序
if __name__ == "__main__":
    main()
