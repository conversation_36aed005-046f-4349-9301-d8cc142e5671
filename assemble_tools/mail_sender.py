#!/usr/bin/python
# -*- coding:utf-8 -*-


import smtplib
from email.header import Head<PERSON>
from email.mime.text import MIMEText
from email.utils import formataddr


#MAIL_USER = '@9snail.com'
#MAIL_PASS = '12'
#MAIL_TO = '@9snail.com'



class mail_sender:
    def __init__(self,
                 mail_from,
                 mail_from_user_name,
                 mail_from_pass,
                 mail_to,
                 title,
                 msg):
        self.mail_from = mail_from
        self.mail_from_user_name = mail_from_user_name
        self.mail_from_pass = mail_from_pass
        self.mail_to = mail_to
        self.title = title
        self.msg = msg

    def send(self):
        ret = False

        try:
            self.msg = self.msg.replace('"', '\'')
            print 'send mail to ' + self.mail_to

            email = MIMEText(self.msg, 'html', 'utf-8')
            email['From'] = formataddr([None, self.mail_from])  # 括号里的对应发件人邮箱昵称、发件人邮箱账号
            email['To'] = self.mail_to  # 括号里的对应收件人邮箱昵称、收件人邮箱账号
            email['Subject'] = Header(self.title,'utf-8')  # 邮件的主题，也可以说是标题

            server = smtplib.SMTP_SSL("smtp.feishu.cn", 465)  # 发件人邮箱中的SMTP服务器，端口是25
            server.set_debuglevel(0)
            server.login(self.mail_from_user_name, self.mail_from_pass)  # 括号中对应的是发件人邮箱账号、邮箱密码
            server.sendmail(self.mail_from, self.mail_to.split(','), email.as_string())  # 括号中对应的是发件人邮箱账号、收件人邮箱账号、发送邮件
            server.quit()
            print 'send mail success'
            ret = True
        except Exception, e:
            print e
        return ret


if __name__ == '__main__':
    mail_sender_obj = mail_sender(MAIL_USER,MAIL_USER,MAIL_PASS, MAIL_TO,'Python email test','<html><h1>你好！</h1></html>')
    mail_sender_obj.send()
