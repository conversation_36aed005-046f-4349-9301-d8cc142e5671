#!/usr/bin/python
# -*- coding:utf-8 -*-

from smb.SMBConnection import SMBConnection

class ConnectSamba():

    def __init__(self, ip, user_name, pass_word):
        self.user_name = user_name
        self.pass_word = pass_word
        self.my_name = self.user_name
        self.domain_name = self.user_name
        self.remote_smb_IP = ip
        self.port = 139
        self.dir = ""

    def downloadFile(self, filename, download_filepath):
        '''
        下载文件
        :param filename: 保存到本地的文件名
        :param download_filepath: 保存到本地文件的路径
        :return:c
        '''
        try:
            conn = SMBConnection(self.user_name, self.pass_word, self.my_name, self.domain_name, use_ntlm_v2=True)
            conn.connect(self.remote_smb_IP, self.port)
            file_obj = open(download_filepath + filename, 'wb')
            conn.retrieveFile(self.dir, filename, file_obj)
            file_obj.close()
            return True
        except:
            return False

    def uploadFile(self, filename, upload_path):
        '''
        上传文件
        :param filename: 上传文件的名称
        :param upload_path: 上传文件的路径
        :return:True or False
        '''
        try:
            conn = SMBConnection(self.user_name, self.pass_word, self.my_name, self.domain_name, use_ntlm_v2=True)
            conn.connect(self.remote_smb_IP, self.port)
            file_obj = open(upload_path + filename, 'rb')
            conn.storeFile(self.dir, filename, file_obj)
            file_obj.close()
            return True
        except:
            return False