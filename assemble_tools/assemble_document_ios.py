#!/usr/bin/python
# -*- coding:utf-8 -*-

import codecs
import os
import webbrowser

import git_helper

'''<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
table{border-right:1px solid #ccc;border-bottom:1px solid #ccc} 
table td{border-left:1px solid #ccc;border-top:1px solid #ccc;padding-left:8px;padding-right:8px;} 
*{font-size:14px; }
.important_text{color:#369; font-weight: bold;}
.log_info{color:#000; }
.normal_info{color:#666; }
.maintitle{font-size: 18px;}
.title{background-color:#369; border:1px solid #369; color:#fff}
    </style>
</head>
<body>
<div><br/> <br/> <br/></div>

<table style='line-height:28px' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' rowspan='5' class='maintitle' width='25%'>测试包详情</td>
        <td class='maintitle' width='25%'>主包</td>
        <td width='50%'><span class='important_text'>"${SVN_CODE}"</span></td>
    </tr>
</table>

<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td class='normal_info' width='25%'>svn地址</td>
        <td class='important_text' width='75%'><a href='"${APK_LINK}"'>"${APK_LINK}"</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>类型</td>
        <td class='important_text' width='75%'>"${APK_TYPE}"</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>渠道</td>
        <td class='important_text' width='75%'>"${CHANNEL_CODE}"</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>VersionCode</td>
        <td width='75%' class='important_text'>"${VERSION_CODE}"</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>VersionName</td>
        <td width='75%' class='important_text'>"${VERSION_NAME}"</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>打包日期</td>
        <td width='75%'>"${PACKAGE_DATE}"</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>文件大小(Byte)</td>
        <td width='75%'>${PACKAGE_SIZE}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>备注</td>
        <td width='75%'>${DEX_INFO}</td>
    </tr>
</table>

<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' colspan='4' class='title' width='100%'>更新说明</td>
    </tr>

    <tr style="background-color:gray">
        <td align='center' width='10%'>序号</td>
        <td align='center' width='40%'>功能</td>
        <td align='center' width='30%'>状态</td>
        <td align='center' width='20%'>负责人</td>
    </tr>
    <tr>
        <td align='center' width='10%'>"$order"</td>
        <td align='center' width='40%'>"$func"</td>

        <td align='center' width='30%'>"$state"</td>

        <td align='center' width='20%'>"$name"</td>
    </tr>
    <tr>
        <td align='center' width='10%'>"$order"</td>
        <td align='center' width='40%'>"$func"</td>

        <td style="background-color:yellow" align='center' width='30%'>"$state"</td>

        <td align='center' width='20%'>"$name"</td>
    </tr>

    <tr>
        <td align='center' width='10%'>"$order"</td>
        <td align='center' width='40%'> </td>
        <td align='center' width='30%'> </td>
        <td align='center' width='20%'> </td>
    </tr>
</table>

<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' colspan='3' class='title' width='100%'>测试重点</td>
    </tr>
    <tr>
        <td>
            </br>
            </br>
            </br>
        </td>
    </tr>
</table>

<table></table>
</body>
</html>
'''


class assemble_document:
    def __init__(self,
                 build_project_root,
                 build_produce_name,
                 build_info,
                 build_dev_address,
                 http_build_dev_address,
                 build_dis_address,
                 build_type,
                 build_channel,
                 build_versionCode,
                 build_versionName,
                 build_package_date,
                 build_package_size,
                 build_remark,
                 build_produce_demand_file,
                 build_test_details_file,
                 build_lina_file,
                 build_lina_file_new,
                 build_infoplist_file,
                 build_version_commit_count,
                 ):
        self._currentDir = os.path.dirname(os.path.abspath(__file__))
        self._currentDirParent = os.path.abspath(os.path.join(self._currentDir, ".."))

        self._build_project_root = build_project_root
        self._build_version_commit_count = build_version_commit_count
        self._build_test_details_file = os.path.join(self._currentDir, build_test_details_file)
        self._build_test_desc_file = os.path.join(self._currentDir, build_produce_demand_file)
        self.build_lina_file = os.path.join(self._currentDirParent, build_lina_file)
        self.build_lina_file_new = os.path.join(self._currentDirParent, build_lina_file_new)
        self.build_infoplist_file = os.path.join(self._currentDirParent, build_infoplist_file)
        self._build_remark = build_remark
        self._build_package_size = build_package_size
        self._build_package_date = build_package_date
        self._build_versionName = build_versionName
        self._build_versionCode = build_versionCode
        self._build_channel = build_channel
        self._build_type = build_type
        self._build_dev_address = build_dev_address
        self._http_build_dev_address = http_build_dev_address
        self._build_dis_address = build_dis_address
        self._build_info = build_info
        self._build_produce_name = build_produce_name

        self._lines = []

    def _addTestDesc(self):
        infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
                                                                                                                     <tr>
                                                                                                                     <td align='center' colspan='4' class='title' width='100%'>产品需求</td>
    </tr>

    <tr style="background-color:gray">
        <td align='center' width='10%'>序号</td>
        <td align='center' width='40%'>功能</td>
        <td align='center' width='30%'>状态</td>
        <td align='center' width='20%'>负责人</td>
    </tr>
</tr>''']
        order = 0
        with codecs.open(self._build_test_desc_file, 'r', 'utf-8') as fp:
            for line in fp:
                line = line.strip()
                if line.startswith('#') or len(line) == 0:
                    continue

                cols = line.split('_')
                if len(cols) != 3:
                    raise Exception('产品需求未按要求格式填写...')
                func, state, name = cols
                order += 1
                item = ''
                if state == u'提测':
                    item = u'''<tr>
        <td align='center' width='10%'>{order}</td>
        <td align='left' width='50%'>{func}</td>
        <td align='center' width='20%'>{state}</td>
        <td align='center' width='20%'>{name}</td>
    </tr>'''
                else:
                    item = u'''<tr>
        <td align='center' width='10%'>{order}</td>
        <td align='left' width='50%'>{func}</td>
        <td style="background-color:yellow" align='center' width='20%'>{state}</td>
        <td align='center' width='20%'>{name}</td>
    </tr>'''
                infos.append(item.format(order=order, func=func, state=state, name=name))

        infos.append(u'''<tr>
        <td align='center' width='10%'> </td>
        <td align='center' width='50%'> </td>
        <td align='center' width='20%'> </td>
        <td align='center' width='20%'> </td>
    </tr>''')
        infos.append(u'''
</table>''')
        return infos

    def _addTestDetails(self):
        infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' colspan='3' class='title' width='100%'>测试重点</td>
    </tr>
    <tr>
        <td>''']
        with codecs.open(self._build_test_details_file, 'r', 'utf-8') as fp:
            for line in fp:
                line = line.strip()
                if line.startswith('#') or len(line) == 0:
                    continue
                infos.append(line + u'</br>')
        infos.append(u'''</td>
    </tr>
</table>''')
        return infos

    def _addVersionCommit(self):
        git_record = git_helper.getGitVersionCommit(self._build_project_root,
                                                    commitCount=self._build_version_commit_count)
        if len(git_record) > 0:
            record = []
            for branchInfo in git_record:
                record.append(u'''<tr style="background-color:gray">
                <td colspan='3' width='100%'>[{type}] {path}======>{branch}</td>
                </tr>'''.format(type=branchInfo['type'],
                                path=branchInfo['path'],
                                branch=branchInfo['branch']))

                for commit in branchInfo['commits']:
                    record.append(u'''<tr>
            <td align='center' width='20%'>{committer}</td>
            <td align='center' width='20%'>{version}</td>
            <td align='center' width='40%'>{time}</td>
        </tr>'''.format(committer=commit['committer'],
                        version=commit['version'],
                        time='{0}({1})'.format(commit['time'], commit['timeago'])))
                    record.append("<tr><td colspan='3' width='100%'>" + commit['info'].replace('\n',
                                                                                               '<br/>').decode(
                        'utf8') + '</td></tr>')
            info = u''.join(record)
        infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
                    <tr>
                        <td align='center' colspan='3' class='title' width='100%'>提交记录</td>
                    </tr>
                     <tr>
                     ''',
                 info,
                 u'''
             </tr>
         </table>''']

        return infos

    def _addCodeVersionCommit(self):
        projectPath = os.path.normpath(os.path.join(self._build_project_root.replace("-Build","").replace("-build",""), "../"))
        git_record = git_helper.getGitVersionCommit(projectPath,commitCount=self._build_version_commit_count)
        if len(git_record) > 0:
            record = []
            for branchInfo in git_record:
                record.append(u'''<tr style="background-color:gray">
                <td colspan='3' width='100%'>[{type}] {path}======>{branch}</td>
                </tr>'''.format(type=branchInfo['type'],
                                path=branchInfo['path'],
                                branch=branchInfo['branch']))

                for commit in branchInfo['commits']:
                    record.append(u'''<tr>
            <td align='center' width='20%'>{committer}</td>
            <td align='center' width='20%'>{version}</td>
            <td align='center' width='40%'>{time}</td>
        </tr>'''.format(committer=commit['committer'],
                        version=commit['version'],
                        time='{0}({1})'.format(commit['time'], commit['timeago'])))
                    record.append("<tr><td colspan='3' width='100%'>" + commit['info'].replace('\n',
                                                                                               '<br/>').decode(
                        'utf8') + '</td></tr>')
            info = u''.join(record)

        infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
                    <tr>
                        <td align='center' colspan='3' class='title' width='100%'>提交记录-代码工程</td>
                    </tr>
                     <tr>
                     ''',
                 info,
                 u'''
             </tr>
         </table>''']

        return infos

    def _addConfigure(self):
        try:
            infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
        <tr>
            <td align='center' colspan='3' class='title' width='100%'>应用配置</td>
        </tr>
        <tr>
            <td>''']
            with codecs.open(self.build_lina_file, 'r', 'utf-8') as fp:
                for line in fp:
                    line = line.strip()
                    infos.append(line + u'</br>')

            infos.append(u'''</td>
        </tr>
    </table>''')
            return infos
        except IOError:
            return ""

    def _addConfigure_new(self):
        try:
            infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
        <tr>
            <td align='center' colspan='3' class='title' width='100%'>应用配置</td>
        </tr>
        <tr>
            <td>''']
            with codecs.open(self.build_lina_file_new, 'r', 'utf-8') as fp:
                for line in fp:
                    line = line.strip()
                    infos.append(line + u'</br>')

            infos.append(u'''</td>
        </tr>
    </table>''')
            return infos
        except IOError:
            return ""

    def _addConfigurePlist(self):
        try:
            infos = [u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
        <tr>
            <td align='center' colspan='3' class='title' width='100%'>Info.Plist</td>
        </tr>
        <tr>
            <td>''']
            with codecs.open(self.build_infoplist_file, 'r', 'utf-8') as fp:
                for line in fp:
                    line = line.strip()
                    line =line.replace("<", "[")
                    line =line.replace(">", "]")
                    line = line + u'</br>'
#                     if line.find("AppLovinSdkKey")>=0:
#                        line = "https://apple.com/" + u'</br>' + line
                    infos.append(line)

            infos.append(u'''</td>
        </tr>

    </table>''')
            return infos
        except IOError:
            return ""

    def build(self):
        self._lines = []
        # add header
        header = u'''<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
table{border-right:1px solid #ccc;border-bottom:1px solid #ccc}
table td{border-left:1px solid #ccc;border-top:1px solid #ccc;padding-left:8px;padding-right:8px;}
*{font-size:14px; }
.important_text{color:#369; font-weight: bold;}
.log_info{color:#000; }
.normal_info{color:#666; }
.maintitle{font-size: 18px;}
.title{background-color:#369; border:1px solid #369; color:#fff}
    </style>
</head>
    <body>
    <div><br/> <br/> <br/></div>'''

        title = u'''<table style='line-height:28px' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td align='center' rowspan='5' class='maintitle' width='25%'>测试包详情</td>
        <td class='maintitle' width='25%'>{BUILD_PRODUCE_NAME}</td>
        <td width='50%'><span class='important_text'>{BUILD_INFO}</span></td>
    </tr>
</table>'''.format(BUILD_PRODUCE_NAME=self._build_produce_name, BUILD_INFO=self._build_info)

        packageInfo = u'''<table style='line-height:28px;margin-top:10px;' cellpadding='0' cellspacing='0' width='800'>
    <tr>
        <td class='normal_info' width='25%'>dev地址</td>
        <td class='important_text' width='75%'><a href='{BUILD_DEV_ADDRESS}'>{BUILD_DEV_ADDRESS}</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>Http的dev地址</td>
        <td class='important_text' width='75%'><a href='{HTTP_BUILD_DEV_ADDRESS}'>{HTTP_BUILD_DEV_ADDRESS}</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>dis地址</td>
        <td class='important_text' width='75%'><a href='{BUILD_DIS_ADDRESS}'>{BUILD_DIS_ADDRESS}</a></td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>类型</td>
        <td class='important_text' width='75%'>{BUILD_TYPE}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>渠道</td>
        <td class='important_text' width='75%'>{BUILD_CHANNEL}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>VersionCode</td>
        <td width='75%' class='important_text'>{BUILD_VERSIONCODE}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>VersionName</td>
        <td width='75%' class='important_text'>{BUILD_VERSIONNAME}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>打包日期</td>
        <td width='75%'>{BUILD_PACKAGE_DATE}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>文件大小(Byte)</td>
        <td width='75%'>{BUILD_PACKAGE_SIZE}</td>
    </tr>
    <tr>
        <td class='normal_info' width='25%'>备注</td>
        <td width='75%'>{BUILD_REMARK}</td>
    </tr>
</table>'''.format(BUILD_DEV_ADDRESS=self._build_dev_address,
                   HTTP_BUILD_DEV_ADDRESS=self._http_build_dev_address,
                   BUILD_DIS_ADDRESS = self._build_dis_address,
                   BUILD_TYPE=self._build_type,
                   BUILD_CHANNEL=self._build_channel,
                   BUILD_VERSIONCODE=self._build_versionCode,
                   BUILD_VERSIONNAME=self._build_versionName,
                   BUILD_PACKAGE_DATE=self._build_package_date,
                   BUILD_PACKAGE_SIZE=self._build_package_size,
                   BUILD_REMARK=self._build_remark, )

        # add tail
        tail = ''' </body>
</html>'''

        self._lines.append(header)
        self._lines.append(title)
        self._lines.append(packageInfo)
        self._lines.extend(self._addTestDesc())
        self._lines.extend(self._addTestDetails())
        if self.build_lina_file != None and self.build_lina_file != "":
            self._lines.extend(self._addConfigure())
        if self.build_lina_file_new != None and self.build_lina_file_new != "":
            self._lines.extend(self._addConfigure_new())
        if self.build_infoplist_file != None and self.build_infoplist_file != "":
            self._lines.extend(self._addConfigurePlist())
        self._lines.extend(self._addCodeVersionCommit())
        self._lines.extend(self._addVersionCommit())
        self._lines.append(tail)

        return u''.join(self._lines)

def getTempDocFile(tempFileName, content):
    try:
        content = content.encode('utf-8')
    except Exception as e:
        pass
    with open(tempFileName, 'w') as fp:
        fp.write(content)
    return tempFileName


def browser(buf):
    try:
        buf = buf.encode('utf-8')
    except Exception as e:
        pass
    tmpfile = 'tmp_sendmail.html'
    with open(tmpfile, 'w') as fp:
        fp.write(buf)

    webbrowser.open(tmpfile)


if __name__ == '__main__':
    pass
