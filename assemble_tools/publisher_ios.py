#!/usr/bin/python
# -*- coding:utf-8 -*-

import getopt
import os
import sys
import time

import create_share_link
import ftp_helper
import git_helper
import mail_sender

FTP_HOST = '***********'
FTP_USERNAME = 'assemble'
FTP_PASSWORD = '][t<fCjMR2'

MAIL_USER = '<EMAIL>'
MAIL_PASS = 'Snail888'
MAIL_TO = '<EMAIL>,<EMAIL>'

class Params:
    def __init__(self):
        opts, args = getopt.getopt(sys.argv[1:]
                                   , "hr:n:t:v:c:d:p:i:"
                                   , ["help"
                                       , 'projectDir='
                                       , 'projectName='
                                       , 'projectBuildType='
                                       , 'projectVersionName='
                                       , 'projectVersionCode='
                                       , 'projectBuildDate='
                                       , 'projectBuildApkPath='
                                       , 'projectBuildDisPath='
                                       , 'commitCount='
                                      ])
        print opts
        print args
        self.projectDir = ''
        self.projectName = ''
        self.projectBuildType = 'release'
        self.projectVersionName = ''
        self.projectVersionCode = ''
        self.projectBuildDate = time.strftime('%Y%m%d%H%M', time.localtime(time.time()))
        self.projectBuildApkPath = ''
        self.commitCount = 30
        self.projectBuildDisPath = None
        for op, value in opts:
            if op in ["-h", '--help']:
                self.usage()
                sys.exit()
            elif op in ['-r', '--projectDir=', '--projectDir']:
                self.projectDir = value
            elif op in ["-n", '--projectName=', '--projectName']:
                self.projectName = value
            elif op in ["-t", '--projectBuildType=', '--projectBuildType']:
                self.projectBuildType = value
            elif op in ["-v", '--projectVersionName=', '--projectVersionName']:
                self.projectVersionName = value
            elif op in ["-c", '--projectVersionCode=' '--projectVersionCode']:
                self.projectVersionCode = value
            elif op in ["-d", '--projectBuildDate=', '--projectBuildDate']:
                self.projectBuildDate = value
            elif op in ["-p", '--projectBuildApkPath=', '--projectBuildApkPath']:
                self.projectBuildApkPath = value
            elif op in ["-i", '--projectBuildDisPath=', '--projectBuildDisPath']:
                self.projectBuildDisPath = value
            elif op in ['--commitCount=', '--commitCount']:
                self.commitCount = value
        # check
        if (self.projectDir is ''
                or self.projectName is ''
                or self.projectVersionName is ''
                or self.projectVersionCode is ''
                or self.projectBuildApkPath is ''
        ):
            self.usage()
            sys.exit()

    def usage(self):
        sys.stdout.write('''python publisher.py
     -r or --projectDir=
     -n or --projectName=
     -t or --projectBuildType=  default release
     -v or --projectVersionName=
     -c or --projectVersionCode=
     -d or --projectBuildDate={projectBuildDate}
     -p or --projectBuildApkPath=
     -i or --projectBuildDisPath=

     eg: python publisher.py -n Camera -t release -v 1.00.00 -c 20 -d 20171111 -p c://project/Camera.apk
'''.format(projectBuildDate=self.projectBuildDate))


def main(params):
    # 开始上传
    ftp = ftp_helper.Ftp()
    ftp.open(FTP_HOST, FTP_USERNAME, FTP_PASSWORD)
    uploadFtpPath = 'Release/ftpapk/snailapk/{0}/{1}/{2}'.format(params.projectName, params.projectVersionName,
                                         os.path.basename(params.projectBuildApkPath))
    print 'start upload apk file ' + uploadFtpPath

    filepath = ftp.uploadfile(uploadFtpPath, params.projectBuildApkPath)
    filepath = filepath.replace('ftp://', 'ftp://')
    print 'upload apk file success:' + filepath

    disFilepath = ""
    if params.projectBuildDisPath and params.projectBuildDisPath != "" and params.projectBuildDisPath != "null":
        uploadFtpPath = 'Release/ftpapk/snailapk/{0}/{1}/{2}'.format(params.projectName, params.projectVersionName,
                                         os.path.basename(params.projectBuildDisPath))
        print 'start upload apk file ' + uploadFtpPath

        disFilepath = ftp.uploadfile(uploadFtpPath, params.projectBuildDisPath)
        disFilepath = disFilepath.replace('ftp://', 'ftp://')
        print 'upload apk file success:' + disFilepath

    mappingFileUrl = ""
    ftp.close()

    uploadFtpPath = "/" + uploadFtpPath
    print("uploadFtpPath:" + str(uploadFtpPath))
    share_link = create_share_link.main(uploadFtpPath, FTP_USERNAME, FTP_PASSWORD)

    build_remark = u'提测人：{0}({1})'.format(git_helper.getUserName(), git_helper.getUserEmail())

    # send mail
    filesize = os.path.getsize(params.projectBuildApkPath)
    assemble_document_obj = None

    import assemble_document_ios
    assemble_document_obj = assemble_document_ios.assemble_document(
        build_project_root=params.projectDir,
        build_produce_name=params.projectName,
        build_info='{0}-{1}-{2}'.format(params.projectVersionName, params.projectVersionCode,
                                        params.projectBuildDate),
        build_dev_address=filepath,
        http_build_dev_address=share_link,
        build_dis_address=disFilepath,
        build_type=params.projectBuildType,
        build_channel='AppStore',
        build_versionCode=params.projectVersionCode,
        build_versionName=params.projectVersionName,
        build_package_date=time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
        build_package_size=filesize,
        build_remark=build_remark,  # 备注
        build_produce_demand_file= unicode('提测说明.txt' , "utf8"),
        build_test_details_file=unicode('测试重点说明.txt' , "utf8"),
        build_lina_file="",
        build_lina_file_new="",
        build_infoplist_file="",
        build_version_commit_count = params.commitCount,
    )

    title = '[提测] {projectName}-{versionName}-{versionCode}' \
        .format(projectName=params.projectName,
                versionName=params.projectVersionName,
                versionCode=params.projectVersionCode)
    mail_sender_obj = mail_sender.mail_sender(MAIL_USER,MAIL_USER,MAIL_PASS, MAIL_TO,title,assemble_document_obj.build())
    mail_sender_obj.send()

    #
    #
    # content_file = assemble_document.getTempDocFile("last_assemble_doc.html", assemble_document_obj.build())
    # print 'start upload doc '
    # uploadFtpPath = 'Release/{0}/{1}/doc_version_{2}.html'.format(params.projectName, params.projectVersionName,
    #                                                               params.projectVersionCode)
    # filepath = ftp.uploadfile(uploadFtpPath, content_file)
    # os.remove(content_file)
    #
    # print 'upload doc file success:' + filepath

if __name__ == '__main__':
    params = Params()
    main(params)
