#!/usr/bin/python
# -*- coding:utf-8 -*-

import getopt
import os
import sys
import time

import assemble_document
import create_share_link
import ftp_helper
import git_helper
import mail_sender

FTP_HOST = '***********'
FTP_USERNAME = 'assemble'
FTP_PASSWORD = '][t<fCjMR2'

MAIL_USER = '<EMAIL>'
MAIL_PASS = '8Aj8Shw9QDiiAPG2'
MAIL_TO = '<EMAIL>'

class Params:
    def __init__(self):
        opts, args = getopt.getopt(sys.argv[1:]
                                   , "hr:n:t:v:c:d:p:"
                                   , ["help"
                                       , 'projectDir='
                                       , 'projectName='
                                       , 'projectBuildType='
                                       , 'projectVersionName='
                                       , 'projectVersionCode='
                                       , 'projectBuildDate='
                                       , 'projectBuildApkPath='
                                       , 'commitCount='
                                      ])
        print opts
        print args
        self.projectDir = ''
        self.projectName = ''
        self.projectBuildType = 'release'
        self.projectVersionName = ''
        self.projectVersionCode = ''
        self.projectBuildDate = time.strftime('%Y%m%d%H%M', time.localtime(time.time()))
        self.projectBuildApkPath = ''
        self.commitCount = 30
        for op, value in opts:
            if op in ["-h", '--help']:
                self.usage()
                sys.exit()
            elif op in ['-r', '--projectDir=', '--projectDir']:
                self.projectDir = value
            elif op in ["-n", '--projectName=', '--projectName']:
                self.projectName = value
            elif op in ["-t", '--projectBuildType=', '--projectBuildType']:
                self.projectBuildType = value
            elif op in ["-v", '--projectVersionName=', '--projectVersionName']:
                self.projectVersionName = value
            elif op in ["-c", '--projectVersionCode=' '--projectVersionCode']:
                self.projectVersionCode = value
            elif op in ["-d", '--projectBuildDate=', '--projectBuildDate']:
                self.projectBuildDate = value
            elif op in ["-p", '--projectBuildApkPath=', '--projectBuildApkPath']:
                self.projectBuildApkPath = value
            elif op in ['--commitCount=', '--commitCount']:
                self.commitCount = value
        # check
        if (self.projectDir is ''
                or self.projectName is ''
                or self.projectVersionName is ''
                or self.projectVersionCode is ''
                or self.projectBuildApkPath is ''
        ):
            self.usage()
            sys.exit()

    def usage(self):
        sys.stdout.write('''python publisher.py
     -r or --projectDir=
     -n or --projectName=
     -t or --projectBuildType=  default release
     -v or --projectVersionName=
     -c or --projectVersionCode=
     -d or --projectBuildDate={projectBuildDate}
     -p or --projectBuildApkPath=

     eg: python publisher.py -n Camera -t release -v 1.00.00 -c 20 -d 20171111 -p c://project/Camera.apk
'''.format(projectBuildDate=self.projectBuildDate))


def main(params):
    # 开始上传
    ftp = ftp_helper.Ftp()
    ftp.open(FTP_HOST, FTP_USERNAME, FTP_PASSWORD)
    uploadFtpPath = 'Release/ftpapk/snailapk/{0}/{1}/{2}'.format(params.projectName, params.projectVersionName,
                                                        os.path.basename(params.projectBuildApkPath))
    print 'start upload apk file ' + uploadFtpPath

    filepath = ftp.uploadfile(uploadFtpPath, params.projectBuildApkPath)
    filepath = filepath.replace('ftp://', 'ftp://')
    print 'upload apk file success:' + filepath

    uploadMappingPath = 'Release/ftpapk/snailapk/{0}/{1}/{2}'.format(params.projectName, params.projectVersionName,"mapping_{0}_{1}_{2}.txt".format(params.projectVersionName, params.projectVersionCode, params.projectBuildDate))
    print 'start upload mapping file ' + uploadMappingPath

    mappingFilePath = os.path.dirname(params.projectBuildApkPath)+"/mapping.txt"
    if not os.path.exists(mappingFilePath):
        mappingFilePath = os.path.dirname(params.projectBuildApkPath)+"/../mapping.txt"
    mappingFileUrl = ftp.uploadfile(uploadMappingPath, mappingFilePath)
    mappingFileUrl = mappingFileUrl.replace('ftp://', 'ftp://')
    print 'upload mapping file success:' + mappingFileUrl
    ftp.close()

    uploadFtpPath = "/" + uploadFtpPath
    uploadMappingPath = "/" + uploadMappingPath
    print("uploadFtpPath:" + str(uploadFtpPath))
    share_link = create_share_link.main(uploadFtpPath, FTP_USERNAME, FTP_PASSWORD)
    mapping_share_link = create_share_link.main(uploadMappingPath, FTP_USERNAME, FTP_PASSWORD)
    print('share_link:' + str(share_link))

    build_remark = u'提测人：{0}({1})'.format(git_helper.getUserName(), git_helper.getUserEmail())

    # send mail
    filesize = os.path.getsize(params.projectBuildApkPath)
    assemble_document_obj = assemble_document.assemble_document(
        build_project_root=params.projectDir,
        build_produce_name=params.projectName,
        build_info='{0}-{1}-{2}'.format(params.projectVersionName, params.projectVersionCode,
                                        params.projectBuildDate),
        build_apk_address=filepath,
        build_mapping_address=mappingFileUrl,
        http_build_apk_address=share_link,
        http_build_mapping_address=mapping_share_link,
        build_type=params.projectBuildType,
        build_channel='',
        build_versionCode=params.projectVersionCode,
        build_versionName=params.projectVersionName,
        build_package_date=time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
        build_package_size=filesize,
        build_remark=build_remark,  # 备注
        build_produce_demand_file= unicode('提测说明.txt' , "utf8"),
        build_test_details_file=unicode('测试重点说明.txt', "utf8"),
        build_cfg_file=unicode('.gradle/configCache/encrypt/', "utf8"),
        build_product_file=unicode('android/product.gradle', "utf8"),
        build_version_commit_count = params.commitCount,
        build_proguard_file = unicode('android/proguard-user.txt', "utf8")
    )

    title = '[提测] {projectName}-{versionName}-{versionCode}' \
        .format(projectName=params.projectName,
                versionName=params.projectVersionName,
                versionCode=params.projectVersionCode)
    sendMsg = assemble_document_obj.build()
    mail_sender_obj = mail_sender.mail_sender(MAIL_USER,MAIL_USER,MAIL_PASS, MAIL_TO,title,sendMsg)
    ret = mail_sender_obj.send()
    if ret :
        print 'end email success'
    else :
        print 'send email failed, try to upload html to FTP'
        ftp2 = ftp_helper.Ftp()
        ftp2.open(FTP_HOST, FTP_USERNAME, FTP_PASSWORD)

        try:
            uploadAssembleDocPath = 'Release/ftpapk/snailapk/{0}/{1}/{2}'.format(params.projectName, params.projectVersionName,"assembleDoc_{0}_{1}_{2}.html".format(params.projectVersionName, params.projectVersionCode, params.projectBuildDate))
            print 'start AssembleDoc file ' + uploadAssembleDocPath
            content_file = assemble_document.getTempDocFile("last_assemble_doc.html", sendMsg)
            assembleDocFileUrl = ftp2.uploadfile(uploadAssembleDocPath, content_file)
            assembleDocFileUrl = mappingFileUrl.replace('ftp://', 'ftp://')
            print 'upload AssembleDoc file success:' + assembleDocFileUrl
            try:
                os.remove(content_file)
            except Exception, e:
                a = 1 #temp
        finally:
            ftp2.close()

if __name__ == '__main__':
    params = Params()
    main(params)
